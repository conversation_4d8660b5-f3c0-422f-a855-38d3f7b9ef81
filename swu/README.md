# SWu IKEv2 Client Implementation

This package provides a complete Go implementation of the SWu IKEv2 client for VoWiFi (Voice over WiFi) authentication and tunnel establishment.

## Overview

The SWu interface is used by UE (User Equipment) to establish an IPSec tunnel with the ePDG (Evolved Packet Data Gateway) for VoWiFi services. This implementation follows the 3GPP specifications and is based on the reference PHP implementation.

## Architecture

### Core Components

- **SWuClient**: Main client implementation managing the complete IKEv2 flow
- **Network**: UDP connection management for ports 500 and 4500 (NAT-T)
- **Transaction**: State management and SPI handling
- **CryptoHelper**: Cryptographic operations (DH, key derivation, encryption)
- **GenericPacket**: IKEv2 packet marshaling/unmarshaling
- **Payloads**: Implementation of all IKEv2 payload types

### Protocol Flow

1. **IKE_SA_INIT Exchange**
   - Client sends SA, KE, Nonce, NAT-D payloads
   - Server responds with SA, KE, Nonce, NAT-D payloads
   - DH shared secret is computed and IKE keys are derived

2. **IKE_AUTH Exchange (EAP-AKA)**
   - Client sends ID payload with NAI (Network Access Identifier)
   - Server responds with EAP-AKA Challenge (RAND, AUTN)
   - Client performs USIM authentication and sends EAP-AKA Response (RES)
   - Server sends EAP Success

3. **Final IKE_AUTH Exchange**
   - Client sends AUTH payload for authentication
   - Server responds with AUTH, SA, CP (Configuration) payloads
   - Configuration includes client IP, P-CSCF IP, and DNS servers

4. **IPSec Tunnel Established**
   - ESP tunnel mode with encryption and integrity protection
   - Client can now communicate with IMS network

## Usage

### Basic Usage

```go
import (
    "context"
    "log/slog"
    "github.com/damonto/vowifi/swu"
    "github.com/damonto/vowifi/usim"
)

// Create logger
logger := slog.New(slog.NewTextHandler(os.Stdout, nil))

// Create USIM instance (requires real USIM card)
usimCard, err := usim.NewUSIM(reader)
if err != nil {
    return err
}

// Create SWu client
ctx := context.Background()
client, err := swu.NewSWuClient(ctx, imei, usimCard, epdgAddress, mark, logger)
if err != nil {
    return err
}

// Run SWu authentication
err = client.Run()
if err != nil {
    return err
}

// Get assigned addresses
clientAddr := client.GetClientAddress()
pcscfAddr := client.GetPCSCFAddress()
```

### Using IKEv2 Wrapper

```go
// Create IKEv2 instance (higher-level interface)
ikev2, err := swu.NewIKEv2(ctx, usimCard, imei, epdgAddress, logger)
if err != nil {
    return err
}

// Start listening and authentication
err = ikev2.Listen()
if err != nil {
    return err
}

// Get configuration
clientAddr := ikev2.GetClientAddress()
pcscfAddr := ikev2.GetPCSCFAddress()

// Clean up
ikev2.Close()
```

## Features

### Implemented Features

- ✅ Complete IKEv2 protocol implementation
- ✅ EAP-AKA authentication with USIM integration
- ✅ NAT-T support (automatic port switching)
- ✅ DH Group 2 (1024-bit MODP) key exchange
- ✅ AES-CBC encryption and HMAC-SHA1 integrity
- ✅ Configuration payload parsing (IP addresses, DNS)
- ✅ Comprehensive logging with slog
- ✅ Proper error handling and timeouts
- ✅ Retransmission logic with exponential backoff
- ✅ Modern Go patterns and interfaces

### Cryptographic Support

- **Encryption**: AES-CBC with 128-bit keys
- **Integrity**: HMAC-SHA1 with 96-bit truncation
- **PRF**: HMAC-SHA1 for key derivation
- **DH Group**: Group 2 (1024-bit MODP)
- **Key Derivation**: IKEv2 standard key derivation functions

### Network Features

- **Dual Port Support**: UDP 500 and 4500 for NAT-T
- **Automatic NAT-T**: Switches to port 4500 when NAT detected
- **Connection Management**: Proper connection lifecycle and cleanup
- **Timeout Handling**: Configurable timeouts with retransmission

## Testing

The package includes comprehensive tests:

```bash
# Run all tests
go test ./swu -v

# Run specific tests
go test ./swu -run TestCryptoHelperDH -v

# Run examples
go test ./swu -run ExampleSWuClient -v
```

### Test Coverage

- ✅ Packet marshaling/unmarshaling
- ✅ Cryptographic operations
- ✅ DH key exchange
- ✅ Security Association payloads
- ✅ Transaction management
- ✅ Network layer (mock tests)

## Configuration

### ePDG Address

The ePDG address can be:
- Manually specified: `"epdg.example.com"`
- Auto-constructed: `"epdg.epc.mnc{MNC}.mcc{MCC}.pub.3gppnetwork.org"`

### USIM Integration

The client automatically retrieves parameters from USIM:
- MCC (Mobile Country Code)
- MNC (Mobile Network Code)
- IMSI (International Mobile Subscriber Identity)
- Authentication parameters (KI, OP, OPC, SQN)

## Dependencies

- Go 1.21+ (uses modern Go features)
- USIM module for authentication
- Standard library crypto packages
- slog for structured logging

## Limitations

- Currently supports only DH Group 2 (can be extended)
- EAP-AKA implementation is basic (can be enhanced)
- IPv4 focus (IPv6 support can be added)
- Single proposal SA (multiple proposals can be added)

## References

- 3GPP TS 24.302: Access to the 3GPP Evolved Packet Core (EPC) via non-3GPP access networks
- RFC 7296: Internet Key Exchange Protocol Version 2 (IKEv2)
- RFC 4187: Extensible Authentication Protocol Method for 3rd Generation Authentication and Key Agreement (EAP-AKA)
- PHP reference implementation in IMSClient project

## Contributing

When contributing to this implementation:

1. Follow Go coding standards and conventions
2. Add comprehensive tests for new features
3. Use structured logging with slog
4. Reference the PHP implementation for protocol correctness
5. Maintain backward compatibility with existing interfaces

## License

This implementation is part of the VoWiFi project and follows the same licensing terms.
