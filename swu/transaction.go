package swu

import (
	"crypto/rand"
	"encoding/binary"
	"fmt"
	"log/slog"
	"time"

	"github.com/damonto/vowifi/usim"
)

// Transaction manages the state of an IKEv2 exchange
type Transaction struct {
	logger *slog.Logger

	// Identity information
	IMEI string
	NAI  string

	// SPI values
	SPIInitiator uint64
	SPIResponder uint64

	// Message ID counter
	MessageID uint32

	// Network addresses
	HostS string // ePDG hostname
	AddrS string // ePDG IP address
	PortS uint16 // ePDG port
	AddrC string // Client IP address
	PortC uint16 // Client port

	// IKE crypto parameters
	IKEEncr       uint16
	IKEEncrKeyLen uint16
	IKEInteg      uint16
	IKEPRF        uint16
	IKEDH         uint16

	// ESP SPI values
	ESPSPIInitiator uint32
	ESPSPIResponder uint32

	// EAP state
	EAPID uint8

	// Nonces
	NonceInitiator []byte
	NonceResponder []byte

	// Crypto helper
	Crypto *CryptoHelper

	// Packet buffer for retransmission
	IKESAInitPacketBuffer []byte
}

// NewTransaction creates a new IKEv2 transaction
func NewTransaction(imei string, nai string, logger *slog.Logger) (*Transaction, error) {
	t := &Transaction{
		logger:    logger,
		IMEI:      imei,
		NAI:       nai,
		MessageID: 0,
		IKEDH:     DHGroup1024MODP,
	}

	// Generate random SPI
	var spiBytes [8]byte
	if _, err := rand.Read(spiBytes[:]); err != nil {
		return nil, fmt.Errorf("failed to generate SPI: %w", err)
	}
	t.SPIInitiator = binary.BigEndian.Uint64(spiBytes[:])

	// Generate random ESP SPI
	var espSpiBytes [4]byte
	if _, err := rand.Read(espSpiBytes[:]); err != nil {
		return nil, fmt.Errorf("failed to generate ESP SPI: %w", err)
	}
	t.ESPSPIInitiator = binary.BigEndian.Uint32(espSpiBytes[:])

	// Generate random nonce
	t.NonceInitiator = make([]byte, 16)
	if _, err := rand.Read(t.NonceInitiator); err != nil {
		return nil, fmt.Errorf("failed to generate nonce: %w", err)
	}

	// Initialize crypto helper
	t.Crypto = NewCryptoHelper(logger)
	t.Crypto.SetCryptoDH(t.IKEDH)

	logger.Info("[Transaction] created",
		"imei", imei,
		"nai", nai,
		"spi_initiator", fmt.Sprintf("0x%016x", t.SPIInitiator),
		"esp_spi_initiator", fmt.Sprintf("0x%08x", t.ESPSPIInitiator))

	return t, nil
}

// SetCryptoMode sets the cryptographic parameters
func (t *Transaction) SetCryptoMode(encr, encrKeyLen, integ, prf uint16) {
	t.IKEEncr = encr
	t.IKEEncrKeyLen = encrKeyLen
	t.IKEInteg = integ
	t.IKEPRF = prf

	t.Crypto.SetCryptoMode(prf, integ, encr, encrKeyLen)

	t.logger.Info("[Transaction] crypto mode set",
		"encr", encr,
		"encr_keylen", encrKeyLen,
		"integ", integ,
		"prf", prf)
}

// SetCryptoSPI sets the SPI values for crypto operations
func (t *Transaction) SetCryptoSPI() {
	t.Crypto.SetSPI(t.SPIInitiator, t.SPIResponder)
}

// SetCryptoNonce sets the nonce values for crypto operations
func (t *Transaction) SetCryptoNonce() {
	t.Crypto.SetNonce(t.NonceInitiator, t.NonceResponder)
}

// ResponderHandler manages response handling with timeouts and retries
type ResponderHandler struct {
	MessageID uint32
	Callback  func(packet *GenericPacket, userdata interface{}) error
	Timestamp time.Time
	Timeout   time.Duration
	Retry     int
	MaxRetry  int
	Userdata  interface{}
}

// ResponderManager manages pending response handlers
type ResponderManager struct {
	logger   *slog.Logger
	handlers map[uint32]*ResponderHandler
	network  *Network
}

// NewResponderManager creates a new responder manager
func NewResponderManager(network *Network, logger *slog.Logger) *ResponderManager {
	return &ResponderManager{
		logger:   logger,
		handlers: make(map[uint32]*ResponderHandler),
		network:  network,
	}
}

// WaitResponder registers a handler for a specific message ID
func (rm *ResponderManager) WaitResponder(messageID uint32, callback func(packet *GenericPacket, userdata interface{}) error, userdata interface{}, timeout time.Duration) {
	handler := &ResponderHandler{
		MessageID: messageID,
		Callback:  callback,
		Timestamp: time.Now(),
		Timeout:   timeout,
		Retry:     0,
		MaxRetry:  5,
		Userdata:  userdata,
	}

	rm.handlers[messageID] = handler
	rm.logger.Debug("[ResponderManager] registered handler", "message_id", messageID, "timeout", timeout)
}

// ProcessResponderPacket processes an incoming response packet
func (rm *ResponderManager) ProcessResponderPacket(packet *GenericPacket) error {
	handler, exists := rm.handlers[packet.MessageID]
	if !exists {
		rm.logger.Warn("[ResponderManager] unhandled response packet", "message_id", packet.MessageID)
		return nil
	}

	// Remove handler and call callback
	delete(rm.handlers, packet.MessageID)

	if err := handler.Callback(packet, handler.Userdata); err != nil {
		return fmt.Errorf("response handler error: %w", err)
	}

	rm.logger.Debug("[ResponderManager] processed response", "message_id", packet.MessageID)
	return nil
}

// ProcessResponderPacketWithData processes an incoming response packet with raw packet data
func (rm *ResponderManager) ProcessResponderPacketWithData(packet *GenericPacket, rawData []byte) error {
	handler, exists := rm.handlers[packet.MessageID]
	if !exists {
		rm.logger.Warn("[ResponderManager] unhandled response packet", "message_id", packet.MessageID)
		return nil
	}

	// Remove handler and call callback with raw data instead of userdata
	delete(rm.handlers, packet.MessageID)

	if err := handler.Callback(packet, rawData); err != nil {
		return fmt.Errorf("response handler error: %w", err)
	}

	rm.logger.Debug("[ResponderManager] processed response", "message_id", packet.MessageID)
	return nil
}

// CheckTimeouts checks for timed out handlers and retries if needed
func (rm *ResponderManager) CheckTimeouts() error {
	now := time.Now()

	for messageID, handler := range rm.handlers {
		if handler.Timeout > 0 && now.Sub(handler.Timestamp) > handler.Timeout {
			if handler.Retry >= handler.MaxRetry {
				delete(rm.handlers, messageID)
				return fmt.Errorf("response timeout for message ID %d after %d retries", messageID, handler.MaxRetry)
			}

			handler.Retry++
			handler.Timestamp = now

			rm.logger.Warn("[ResponderManager] retransmitting packet",
				"message_id", messageID,
				"retry", handler.Retry)

			// Retransmit packet if userdata contains retransmit function
			if retransmitData, ok := handler.Userdata.(RetransmitData); ok {
				if err := retransmitData.RetransmitFunc(retransmitData.PacketData); err != nil {
					rm.logger.Error("[ResponderManager] retransmit failed", "error", err)
				}
			}
		}
	}

	return nil
}

// RetransmitData contains data needed for packet retransmission
type RetransmitData struct {
	RetransmitFunc func([]byte) error
	PacketData     []byte
}

// AuthResult represents the result of USIM authentication
type AuthResult struct {
	RES []byte
	CK  []byte
	IK  []byte
}

// PerformUSIMAuth performs 3G authentication using USIM
func (t *Transaction) PerformUSIMAuth(usimCard *usim.USIM, rand, autn []byte) (*AuthResult, error) {
	t.logger.Info("[Transaction] performing USIM authentication")

	res, ik, ck, err := usimCard.Auth(rand, autn)
	if err != nil {
		return nil, fmt.Errorf("USIM authentication failed: %w", err)
	}

	if len(res) == 0 || len(ik) == 0 || len(ck) == 0 {
		return nil, fmt.Errorf("USIM authentication returned empty values")
	}

	result := &AuthResult{
		RES: res,
		CK:  ck,
		IK:  ik,
	}

	t.logger.Info("[Transaction] USIM authentication successful",
		"res_len", len(res),
		"ck_len", len(ck),
		"ik_len", len(ik))

	return result, nil
}

// IncrementMessageID increments the message ID counter
func (t *Transaction) IncrementMessageID() {
	t.MessageID++
	t.logger.Debug("[Transaction] incremented message ID", "message_id", t.MessageID)
}
