package swu

import (
	"context"
	"fmt"
	"log/slog"
	"net"
	"sync"
	"time"
)

// NetworkType represents the type of network connection
type NetworkType int

const (
	NetworkTypeNAT NetworkType = iota
	NetworkTypeNATT
)

func (n NetworkType) String() string {
	switch n {
	case NetworkTypeNAT:
		return "NAT"
	case NetworkTypeNATT:
		return "NAT-T"
	default:
		return "Unknown"
	}
}

// PacketHandler is a function type for handling incoming packets
type PacketHandler func(from NetworkType, data []byte) error

// Network manages UDP connections for IKEv2 communication
type Network struct {
	ctx      context.Context
	logger   *slog.Logger
	epdgAddr string

	// UDP connections
	natConn  *net.UDPConn // Port 500 connection
	nattConn *net.UDPConn // Port 4500 connection

	// Remote addresses
	natRemote  *net.UDPAddr
	nattRemote *net.UDPAddr

	// Control channels
	stopCh chan struct{}
	wg     sync.WaitGroup

	// Packet handler
	handler PacketHandler
}

// NewNetwork creates a new Network instance
func NewNetwork(ctx context.Context, epdgAddr string, logger *slog.Logger) (*Network, error) {
	n := &Network{
		ctx:      ctx,
		logger:   logger,
		epdgAddr: epdgAddr,
		stopCh:   make(chan struct{}),
	}

	return n, nil
}

// Dial establishes connections to the ePDG
func (n *Network) Dial() error {
	// Resolve ePDG address
	ips, err := net.LookupIP(n.epdgAddr)
	if err != nil {
		return fmt.Errorf("failed to resolve ePDG address %s: %w", n.epdgAddr, err)
	}

	if len(ips) == 0 {
		return fmt.Errorf("no IP addresses found for ePDG %s", n.epdgAddr)
	}

	epdgIP := ips[0]
	n.logger.Info("[Network] resolved ePDG address", "hostname", n.epdgAddr, "ip", epdgIP.String())

	// Set up remote addresses
	n.natRemote = &net.UDPAddr{IP: epdgIP, Port: 500}
	n.nattRemote = &net.UDPAddr{IP: epdgIP, Port: 4500}

	// Create NAT connection (port 500)
	n.natConn, err = net.DialUDP("udp", nil, n.natRemote)
	if err != nil {
		return fmt.Errorf("failed to create NAT connection: %w", err)
	}

	// Create NAT-T connection (port 4500)
	n.nattConn, err = net.DialUDP("udp", nil, n.nattRemote)
	if err != nil {
		n.natConn.Close()
		return fmt.Errorf("failed to create NAT-T connection: %w", err)
	}

	n.logger.Info("[Network] connections established",
		"nat_local", n.natConn.LocalAddr().String(),
		"nat_remote", n.natConn.RemoteAddr().String(),
		"natt_local", n.nattConn.LocalAddr().String(),
		"natt_remote", n.nattConn.RemoteAddr().String())

	return nil
}

// ListenPacket starts listening for packets on both connections
func (n *Network) ListenPacket(handler PacketHandler) {
	n.handler = handler

	// Start listening on NAT connection
	n.wg.Add(1)
	go n.listenNAT()

	// Start listening on NAT-T connection
	n.wg.Add(1)
	go n.listenNATT()

	n.logger.Info("[Network] started packet listeners")
}

// listenNAT listens for packets on the NAT connection (port 500)
func (n *Network) listenNAT() {
	defer n.wg.Done()

	buffer := make([]byte, 1500)

	for {
		select {
		case <-n.stopCh:
			return
		case <-n.ctx.Done():
			return
		default:
			// Set read timeout
			n.natConn.SetReadDeadline(time.Now().Add(100 * time.Millisecond))

			size, err := n.natConn.Read(buffer)
			if err != nil {
				if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
					continue // Timeout is expected, continue listening
				}
				n.logger.Error("[Network] NAT read error", "error", err)
				continue
			}

			if n.handler != nil {
				if err := n.handler(NetworkTypeNAT, buffer[:size]); err != nil {
					n.logger.Error("[Network] NAT packet handler error", "error", err)
				}
			}
		}
	}
}

// listenNATT listens for packets on the NAT-T connection (port 4500)
func (n *Network) listenNATT() {
	defer n.wg.Done()

	buffer := make([]byte, 1500)

	for {
		select {
		case <-n.stopCh:
			return
		case <-n.ctx.Done():
			return
		default:
			// Set read timeout
			n.nattConn.SetReadDeadline(time.Now().Add(100 * time.Millisecond))

			size, err := n.nattConn.Read(buffer)
			if err != nil {
				if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
					continue // Timeout is expected, continue listening
				}
				n.logger.Error("[Network] NAT-T read error", "error", err)
				continue
			}

			if n.handler != nil {
				if err := n.handler(NetworkTypeNATT, buffer[:size]); err != nil {
					n.logger.Error("[Network] NAT-T packet handler error", "error", err)
				}
			}
		}
	}
}

// SendNAT sends data on the NAT connection (port 500)
func (n *Network) SendNAT(data []byte) error {
	if n.natConn == nil {
		return fmt.Errorf("NAT connection not established")
	}

	_, err := n.natConn.Write(data)
	if err != nil {
		return fmt.Errorf("failed to send NAT packet: %w", err)
	}

	n.logger.Debug("[Network] sent NAT packet", "size", len(data))
	return nil
}

// SendNATT sends data on the NAT-T connection (port 4500) with Non-ESP marker
func (n *Network) SendNATT(data []byte) error {
	if n.nattConn == nil {
		return fmt.Errorf("NAT-T connection not established")
	}

	// Prepend Non-ESP marker (4 zero bytes)
	packet := make([]byte, 4+len(data))
	copy(packet[4:], data)

	_, err := n.nattConn.Write(packet)
	if err != nil {
		return fmt.Errorf("failed to send NAT-T packet: %w", err)
	}

	n.logger.Debug("[Network] sent NAT-T packet", "size", len(packet))
	return nil
}

// GetNATLocalAddr returns the local address of the NAT connection
func (n *Network) GetNATLocalAddr() net.Addr {
	if n.natConn == nil {
		return nil
	}
	return n.natConn.LocalAddr()
}

// GetNATTLocalAddr returns the local address of the NAT-T connection
func (n *Network) GetNATTLocalAddr() net.Addr {
	if n.nattConn == nil {
		return nil
	}
	return n.nattConn.LocalAddr()
}

// Close closes all connections and stops listeners
func (n *Network) Close() error {
	close(n.stopCh)

	var err error
	if n.natConn != nil {
		if closeErr := n.natConn.Close(); closeErr != nil {
			err = closeErr
		}
	}

	if n.nattConn != nil {
		if closeErr := n.nattConn.Close(); closeErr != nil {
			err = closeErr
		}
	}

	n.wg.Wait()
	n.logger.Info("[Network] connections closed")

	return err
}
